from flask import Flask, request, jsonify
import io
import os
import logging
import json
import traceback

# Import our organized modules
from pdf_processing import extract_text_from_pdf, convert_pdf_to_markdown
from text_sanitization import (
    convert_markdown_to_plain_text_with_library,
    preprocess_pymupdf_text,
    comprehensive_ocr_sanitization,
    normalize_text,
    sanitize_mistral_invoice_text,
    minimal_pymupdf_cleanup
)
from vendor_schemas import (
    get_all_vendor_schemas,
    get_vendor_schema_by_name,
    generate_vendor_schemas_sse
)

app = Flask(__name__)

# --- Production Logging Configuration ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
app.logger.setLevel(logging.INFO)

# --- Configuration for PDF processing ---
MIN_TEXT_THRESHOLD_DEFAULT = 20
DPI_FOR_OCR_DEFAULT = 300
OCR_LANGUAGE_DEFAULT = "eng"

@app.route('/extract_text_original', methods=['POST'])
def extract_text_original():
    """Extract text from PDF using PyMuPDF with OCR fallback."""
    try:
        if 'file' not in request.files:
            return jsonify({"error": "No file uploaded"}), 400
        
        uploaded_file = request.files['file']
        if uploaded_file.filename == '':
            return jsonify({"error": "No file selected"}), 400
        
        pdf_bytes = uploaded_file.read()
        processed_doc, original_doc = extract_text_from_pdf(pdf_bytes)
        
        plain_text_extracted = processed_doc.get_text()
        
        # Clean up
        processed_doc.close()
        original_doc.close()
        
        return jsonify({
            "extracted_text": plain_text_extracted,
            "status": "success"
        })
        
    except Exception as e:
        app.logger.error(f"Error in extract_text_original: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/extract_markdown_ocr', methods=['POST'])
def extract_markdown_ocr():
    """Extract and convert PDF to markdown using PyMuPDF4LLM."""
    try:
        if 'file' not in request.files:
            return jsonify({"error": "No file uploaded"}), 400
        
        uploaded_file = request.files['file']
        if uploaded_file.filename == '':
            return jsonify({"error": "No file selected"}), 400
        
        # Extract OCR parameters
        min_text_threshold = int(request.form.get('min_text_threshold', MIN_TEXT_THRESHOLD_DEFAULT))
        dpi_for_ocr = int(request.form.get('dpi_for_ocr', DPI_FOR_OCR_DEFAULT))
        ocr_language = request.form.get('ocr_language', OCR_LANGUAGE_DEFAULT)
        
        pdf_bytes = uploaded_file.read()
        processed_doc, original_doc = extract_text_from_pdf(
            pdf_bytes, min_text_threshold, dpi_for_ocr, ocr_language
        )
        
        # Convert to markdown
        markdown_chunks_raw = convert_pdf_to_markdown(processed_doc)

        # Extract original text from processed document
        original_text_parts = []
        for page_num in range(processed_doc.page_count):
            page = processed_doc.load_page(page_num)
            original_text_parts.append(page.get_text())
        original_extracted_text = "\n\n".join(original_text_parts)

        # Clean up
        processed_doc.close()
        original_doc.close()

        # Return in the requested structure
        return jsonify([
            {
                "llm_friendly_markdown_output": {
                    "markdown_chunks": markdown_chunks_raw
                },
                "original_extracted_text": original_extracted_text
            }
        ])
        
    except Exception as e:
        app.logger.error(f"Error in extract_markdown_ocr: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/extract_text', methods=['POST'])
def extract_text():
    """Extract text from PDF using PyMuPDF with OCR fallback - simplified endpoint."""
    try:
        if 'file' not in request.files:
            return jsonify({"error": "No file uploaded"}), 400

        uploaded_file = request.files['file']
        if uploaded_file.filename == '':
            return jsonify({"error": "No file selected"}), 400

        pdf_bytes = uploaded_file.read()
        processed_doc, original_doc = extract_text_from_pdf(pdf_bytes)

        plain_text_extracted = processed_doc.get_text()

        # Clean up
        processed_doc.close()
        original_doc.close()

        return jsonify({
            "text": plain_text_extracted,
            "status": "success"
        })

    except Exception as e:
        app.logger.error(f"Error in extract_text: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/sanitize_texts', methods=['POST'])
def sanitize_texts_endpoint():
    """Main text sanitization endpoint."""
    try:
        app.logger.info("Received request for /sanitize_texts")
        
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400
        
        data = request.get_json()
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400
        
        # Check if this is dual-source data (Mistral + PyMuPDF)
        if isinstance(data, list) and len(data) >= 2:
            return _process_dual_source_data(data)
        
        # Process single text input
        input_text = data.get('text', '')
        if not input_text:
            return jsonify({"error": "No text provided for sanitization"}), 400
        
        result = _process_single_text(input_text)
        return jsonify(result)
        
    except Exception as e:
        app.logger.error(f"Error in sanitize_texts_endpoint: {e}")
        return jsonify({"error": str(e)}), 500

def _process_single_text(input_text, options=None):
    """Process a single text input through the sanitization pipeline."""
    if options is None:
        options = {}
    
    response_data = {}
    
    try:
        # Determine input type and process accordingly
        if input_text.strip().startswith('##') or '**' in input_text:
            # Likely markdown input
            sanitized_text = convert_markdown_to_plain_text_with_library(input_text)
            response_data["input_type"] = "markdown"
        else:
            # Plain text - apply PyMuPDF preprocessing
            sanitized_text = preprocess_pymupdf_text(input_text)
            response_data["input_type"] = "plain_text"
        
        # Apply comprehensive sanitization
        final_sanitized_text = comprehensive_ocr_sanitization(
            sanitized_text, 
            apply_intelligent_boilerplate=True
        )
        
        # Build response
        response_data.update({
            "original_text": input_text,
            "sanitized_text": final_sanitized_text,
            "original_length": len(input_text),
            "sanitized_length": len(final_sanitized_text),
            "reduction_percentage": round(
                ((len(input_text) - len(final_sanitized_text)) / len(input_text)) * 100, 2
            ) if len(input_text) > 0 else 0,
            "status": "success"
        })
        
        return response_data
        
    except Exception as e:
        app.logger.error(f"Error in _process_single_text: {e}")
        return {
            "error": f"Text processing error: {str(e)}",
            "status": "error"
        }

def _process_dual_source_data(data_input_array):
    """Process dual-source data (Mistral + PyMuPDF) comparison."""
    try:
        if len(data_input_array) < 2:
            return jsonify({"error": "Dual-source processing requires at least 2 data objects"}), 400
        
        # Extract data from both sources
        mistral_data = data_input_array[0]
        pymupdf_data = data_input_array[1]
        
        # Extract Mistral text from pages markdown
        original_mistral_text_input = ""
        if "pages" in mistral_data and mistral_data["pages"]:
            # Combine markdown from all pages
            markdown_texts = []
            for page in mistral_data["pages"]:
                if "markdown" in page:
                    markdown_texts.append(page["markdown"])
            original_mistral_text_input = "\n\n".join(markdown_texts)
        
        # Extract PyMuPDF text from original_extracted_text
        original_pymupdf_text_input = ""
        if "original_extracted_text" in pymupdf_data:
            original_pymupdf_text_input = pymupdf_data["original_extracted_text"]
        
        # Get additional data to pass through
        mistral_doc_annotation = mistral_data.get("document_annotation")
        pymupdf_llm_output = pymupdf_data.get("llm_friendly_markdown_output")
        
        # Handle JSON parsing for Mistral annotation if it's a string
        if isinstance(mistral_doc_annotation, str):
            try:
                mistral_doc_annotation = json.loads(mistral_doc_annotation)
            except json.JSONDecodeError:
                app.logger.warning("Mistral document_annotation (string) not valid JSON. Passing as raw string.")
        
        app.logger.info(f"Processing dual-source data - Mistral: {len(original_mistral_text_input)} chars, PyMuPDF: {len(original_pymupdf_text_input)} chars")
        
        # Process Mistral text with invoice-specific sanitization
        sanitized_mistral_text = sanitize_mistral_invoice_text(original_mistral_text_input)
        
        # Process PyMuPDF text (plain text) - use minimal cleanup since it's already clean
        sanitized_pymupdf_text = minimal_pymupdf_cleanup(original_pymupdf_text_input)
        
        app.logger.info(f"Sanitized dual-source data - Mistral: {len(sanitized_mistral_text)} chars, PyMuPDF: {len(sanitized_pymupdf_text)} chars")
        
        # Return sanitized texts and pass through original data
        return jsonify({
            "sanitized_mistral_text": sanitized_mistral_text,
            "sanitized_pymupdf_text": sanitized_pymupdf_text,
            "original_mistral_text": original_mistral_text_input,
            "original_pymupdf_text": original_pymupdf_text_input,
            "mistral_document_annotation": mistral_doc_annotation,
            "pymupdf_llm_markdown_output": pymupdf_llm_output,
            "status": "dual_source_success"
        })
        
    except Exception as e:
        app.logger.error(f"Error in _process_dual_source_data: {e}")
        return jsonify({
            "error": f"Dual-source processing error: {str(e)}",
            "status": "dual_source_error"
        }), 500

@app.route('/vendor_schemas', methods=['GET'])
def get_all_vendor_schemas_endpoint():
    """Get all vendor schemas."""
    try:
        schemas = get_all_vendor_schemas()
        return jsonify(schemas)
    except Exception as e:
        app.logger.error(f"Error getting vendor schemas: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/vendor_schemas/<string:vendor_name>', methods=['GET'])
def get_vendor_schema_by_name_endpoint(vendor_name):
    """Get vendor schema by name."""
    try:
        schema = get_vendor_schema_by_name(vendor_name)
        if schema:
            return jsonify(schema)
        else:
            return jsonify({"error": "Vendor schema not found"}), 404
    except Exception as e:
        app.logger.error(f"Error getting vendor schema: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/vendor_schemas_sse')
def vendor_schemas_sse():
    """Server-sent events endpoint for vendor schemas."""
    try:
        return generate_vendor_schemas_sse()
    except Exception as e:
        app.logger.error(f"Error in vendor schemas SSE: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        "status": "healthy",
        "service": "PDF OCR Sanitization API",
        "version": "1.0"
    })

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors."""
    return jsonify({"error": "Endpoint not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors."""
    return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    # Production configuration
    port = int(os.environ.get('PORT', 5000))
    debug_mode = os.environ.get('FLASK_ENV') == 'development'
    
    app.run(
        host='0.0.0.0', 
        port=port, 
        debug=debug_mode
    ) 